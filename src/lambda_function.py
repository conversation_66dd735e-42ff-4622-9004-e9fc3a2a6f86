from contextlib import ExitStack
import json
from concurrent.futures import ThreadPoolExecutor
from functools import partial
import boto3
import sys
import os
from datetime import datetime
from bson import ObjectId
import requests

# For local testing, add layer path
if os.environ.get('AWS_SAM_LOCAL'):
    sys.path.append('/opt/python')
    sys.path.append('/opt/python/python')
else:
    sys.path.append('/opt/python')
    sys.path.append('/opt/python/python')

from aegisGrader import doRubric, doParallelAnswerSheet
from parallels import Manager
from mongoOps import get_mongo_client, create_grading_document, update_answer_sheet_in_document, update_processing_stats

# Use default credential chain (AWS CLI for local, IAM role for Lambda)
s3_client = boto3.client('s3')
                    

def process_files(data):
    answer_sheet_keys = []
    question_paper_key = None
    rubric_key = None

    for file in data['files']:
        purpose = file.get('filePurpose')
        key = file.get('key')
        if purpose == 'answer_sheet':
            answer_sheet_keys.append(key)
        elif purpose == 'question_paper':
            question_paper_key = key
        elif purpose == 'rubric':
            rubric_key = key

    return {
        "answer_sheet_keys": answer_sheet_keys,
        "question_paper_key": question_paper_key,
        "rubric_key": rubric_key
    }

def download_pdf(bucket, key):
    response = s3_client.get_object(Bucket=bucket, Key=key)
    pdf_bytes = response['Body'].read()
    return pdf_bytes

def lambda_handler(event, context):
    records = event["Records"]
    # print("Event => ", event)

    for record in records:
        body = json.loads(record["body"])
        s3 = body["Records"][0]["s3"]
        bucket = s3["bucket"]["name"]
        key = s3["object"]["key"]

        # Fetch and parse the manifest JSON from S3
        print(f"[Mehul] Debug got key: {key} in bucket: {bucket}")
        response = s3_client.get_object(Bucket=bucket, Key=key)
        # print("Response => ", response)
        content = response['Body'].read().decode('utf-8')
        data = json.loads(content)

        # Get the keys for the PDFs
        result = process_files(data)
        answer_sheet_keys = result["answer_sheet_keys"]
        question_paper_key = result["question_paper_key"]
        rubric_key = result["rubric_key"]

        # Download PDFs and store in variable (ram memory)
        pdfs = {}

        if question_paper_key:
            pdfs['question_paper'] = download_pdf(bucket, question_paper_key)
        if rubric_key:
            pdfs['rubric'] = download_pdf(bucket, rubric_key)
        pdfs['answer_sheets'] = []
        # answerSheetBytesTupleList = []
        for ans_key in answer_sheet_keys:
            pdfs['answer_sheets'].append((download_pdf(bucket, ans_key), ans_key))

        with ExitStack() as _:
            subject = data['testDetails']['subject']

            client = get_mongo_client()
            # Create the initial grading document
            document_id = create_grading_document(data, client)
            if not document_id:
                print("Failed to create initial grading document")
                return {"statusCode": 500, "body": json.dumps("Failed to create grading document")}


            rubricFileName = doRubric(pdfs.get('rubric'), pdfs.get('question_paper'), subject, rubric_key if rubric_key else question_paper_key)
            numThreads = 10
            evaluated_results = []
            answerSheetFilesBytesTuple = pdfs.get('answer_sheets', [])
            
            # Counters for tracking results
            successful_count = 0
            error_count = 0

            # Define callback function to save results immediately
            def save_result_callback(result):
                nonlocal successful_count, error_count
                evaluation_text, answer_sheet_key = result

                # Track success/error counts
                if evaluation_text.startswith("Error:") or evaluation_text.startswith("ERROR"):
                    error_count += 1
                    print(f"⚠️  Error processing answer sheet {answer_sheet_key}: {evaluation_text}")
                else:
                    successful_count += 1
                    print(f"✅ Successfully processed answer sheet {answer_sheet_key}")

                # Update this answer sheet in the existing document immediately
                mongo_success = update_answer_sheet_in_document(document_id, data, result, client)
                if mongo_success:
                    print(f"✅ Successfully updated answer sheet in MongoDB document {document_id}")
                else:
                    print(f"❌ Failed to update answer sheet in MongoDB document {document_id}")

                evaluated_results.append(result[0])  # Keep the original list for return value

            # Create manager with callback for immediate saving
            man = Manager(doParallelAnswerSheet, list(zip(answerSheetFilesBytesTuple, [(rubricFileName, subject) for _ in range(len(answerSheetFilesBytesTuple))])), numThreads, result_callback=save_result_callback)
            results = man.processLoop()

            # Update processing stats in the document
            update_processing_stats(document_id, successful_count, error_count, client)

            # Results are already processed by the callback, but we still get them here for completeness

            print(f"📊 Processing Summary for document {document_id}:")
            print(f"   ✅ Successful evaluations: {successful_count}")
            print(f"   ⚠️  Failed evaluations: {error_count}")
            print(f"   📄 Total answer sheets: {successful_count + error_count}")
            client.close()
        return {"statusCode": 200, "body": json.dumps(evaluated_results)}

    return {"statusCode": 200}
