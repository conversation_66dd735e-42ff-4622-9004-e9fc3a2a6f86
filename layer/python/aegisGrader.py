import json
import time
import sys
import traceback
from contextlib import ExitStack
from tempfile import NamedTemporaryFile
from processOCR import process_all_pages, combine_and_evaluate, create_rubric, extract_content_from_docs
# from model import batchProcessing
from mongoOps import get_mongo_client


PDF_BASE64_PREFIX = "data:application/pdf;base64,"

# Removed _TemporaryFileWrapper import - no longer needed with optimized workflow

# Utility to save PDF bytes to a temp file
def save_pdf_bytes_to_tempfile(pdf_bytes, suffix):
    temp_pdf_file = NamedTemporaryFile(mode="wb", suffix=f"_{suffix}.pdf", delete=False)
    temp_pdf_file.write(pdf_bytes)
    temp_pdf_file.flush()
    return temp_pdf_file

def doRubric(rubricPdfBytes, questionPaperPdfBytes=None, subject=None, jobId=None, save_md_files=False):
    """
    Process the rubric PDF bytes and return rubric content directly.
    Optimized to return content instead of file paths.
    """
    client = get_mongo_client()
    if not rubricPdfBytes:
        if (not questionPaperPdfBytes):
            raise ValueError("No rubric PDF bytes provided.")
        print("No rubric provided. Generating rubric from question paper...", file=sys.stderr)
        temp_qp_pdf = save_pdf_bytes_to_tempfile(questionPaperPdfBytes, "question_paper")
        # Process question paper to get document objects directly
        question_paper_docs = process_all_pages(temp_qp_pdf.name, "question_paper", subject, jobId,client)
        # Generate rubric content directly from documents
        rubric_content = create_rubric(question_paper_docs, save_md_files)
        return rubric_content
    temp_rubric_pdf = save_pdf_bytes_to_tempfile(rubricPdfBytes, "rubric")
    # Process rubric to get document objects directly
    rubric_docs = process_all_pages(temp_rubric_pdf.name, "rubric", subject, jobId, client)
    # Extract content directly from documents
    rubric_content = extract_content_from_docs(rubric_docs)
    client.close()
    return rubric_content

def doParallelAnswerSheet(answerSheetBytesKeyTuple, rubricContentAndSubject, child_conn):
    """
    Process the answer sheet PDF bytes against the rubric content.
    Optimized to work with direct content instead of file paths.
    Returns evaluation content as a string.
    """
    answerSheetKey = ""
    try:
        rubric_content = rubricContentAndSubject[0]
        subject = rubricContentAndSubject[1]
        answerSheetBytes = answerSheetBytesKeyTuple[0]
        answerSheetKey = answerSheetBytesKeyTuple[1]
        # print(f"got answerSheetKey: {answerSheetKey}, got answer sheet bytes type: {answerSheetBytes}")
        if not answerSheetBytes:
            raise ValueError("No answer sheet PDF bytes provided.")

        temp_answer_sheet_pdf = save_pdf_bytes_to_tempfile(answerSheetBytes, "answers")
        client = get_mongo_client()
        # Process answer sheet to get document objects directly
        answer_sheet_docs = process_all_pages(temp_answer_sheet_pdf.name, "answer_sheet", subject, answerSheetKey,client)

        print(f"Evaluating answer sheet against rubric...", file=sys.stderr)
        # Pass document objects and content directly
        evaluation_result_doc = combine_and_evaluate(answer_sheet_docs, rubric_content, subject, answerSheetKey,client)
        client.close()
        if not hasattr(evaluation_result_doc, 'page_content'):
            raise ValueError("Evaluation result lacks 'page_content'. Cannot proceed.")

        child_conn.send((evaluation_result_doc.page_content, answerSheetKey)) # Return the evaluation content as a string
    except Exception as e:
        print(f"Error processing answer sheet, e: {e}")
        child_conn.send(("Error: processing answer sheet, please try again", answerSheetKey))

# Keep the old CLI entry point for local testing if needed
if __name__ == "__main__":
    print("This script is now intended to be used as a module for Lambda integration.", file=sys.stderr)
